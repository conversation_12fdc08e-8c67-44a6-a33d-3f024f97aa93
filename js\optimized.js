/**
 * EDEN Pharmaceuticals - Optimized JavaScript
 * This file contains all necessary functionality in a single, optimized file
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize performance optimizations first
    initPerformanceOptimizations();

    // Initialize image optimization
    initImageOptimization();

    // Initialize mobile menu
    initMobileMenu();

    // Initialize back to top button
    initBackToTop();

    // Initialize product animations if products exist
    if (document.querySelectorAll('.product-item').length > 0) {
        initProductAnimations();
    }

    // Initialize feature animations if features exist
    if (document.querySelectorAll('.feature').length > 0) {
        initFeatureAnimations();
    }

    // Initialize carousel if it exists
    const carousel = document.querySelector('.carousel');
    if (carousel) {
        initCarousel({
            autoAdvanceInterval: 3000,
            pauseOnHover: false
        });
    }

    // Initialize stats counter if stats exist
    if (document.querySelectorAll('.stat-number').length > 0) {
        initStatsCounter();
    }

    // Initialize FAQ accordion if it exists
    if (document.querySelectorAll('.faq-item').length > 0) {
        initFaqAccordion();
    }

    // Initialize value items animations if they exist
    if (document.querySelectorAll('.value-item').length > 0) {
        initValueAnimations();
    }

    // Initialize team member animations if they exist
    if (document.querySelectorAll('.team-member').length > 0) {
        initTeamAnimations();
    }

    // Initialize contact form if it exists
    if (document.querySelector('.contact-form')) {
        initContactForm();
    }

    // Initialize location card animations if they exist
    if (document.querySelectorAll('.location-card, .contact-card').length > 0) {
        initLocationCardAnimations();
    }

    // Initialize plant tour parallax if it exists
    if (document.querySelector('.plant-cta')) {
        initPlantTourParallax();
    }

    // Initialize image enlargement functionality
    initImageEnlargement();

    // Initialize footer dropdowns
    initFooterDropdowns();
});

/**
 * Initialize performance optimizations
 */
function initPerformanceOptimizations() {
    // Preload critical resources
    const criticalResources = [
        'css/optimized.css',
        'images/Eden logo PNG.png'
    ];

    criticalResources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = resource.endsWith('.css') ? 'style' : 'image';
        link.href = resource;
        document.head.appendChild(link);
    });

    // Optimize scroll performance
    let ticking = false;
    function updateScrollPosition() {
        // Throttle scroll events for better performance
        if (!ticking) {
            requestAnimationFrame(() => {
                // Handle scroll-based animations here
                ticking = false;
            });
            ticking = true;
        }
    }

    window.addEventListener('scroll', updateScrollPosition, { passive: true });
}

/**
 * Initialize image optimization and lazy loading enhancements
 */
function initImageOptimization() {
    // Enhanced lazy loading for images
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;

                    // Don't modify opacity if image is already loaded
                    if (!img.complete) {
                        // Add loading animation only for unloaded images
                        img.style.opacity = '0';
                        img.style.transition = 'opacity 0.3s ease';

                        // Load the image
                        img.onload = () => {
                            img.style.opacity = '1';
                            img.classList.add('loaded');
                        };

                        img.onerror = () => {
                            console.error('Lazy loading failed for:', img.src);
                            img.style.opacity = '1'; // Show the broken image
                        };
                    } else {
                        // Image already loaded, just show it
                        img.style.opacity = '1';
                        img.classList.add('loaded');
                    }

                    // Stop observing this image
                    imageObserver.unobserve(img);
                }
            });
        }, {
            rootMargin: '100px 0px', // Increased margin for earlier loading
            threshold: 0.01 // Lower threshold for earlier triggering
        });

        // Observe all lazy-loaded images
        document.querySelectorAll('img[loading="lazy"]').forEach(img => {
            // Set initial state for lazy images
            if (!img.complete) {
                img.style.opacity = '0';
            }
            imageObserver.observe(img);
        });
    } else {
        // Fallback for browsers without IntersectionObserver
        document.querySelectorAll('img[loading="lazy"]').forEach(img => {
            img.style.opacity = '1';
        });
    }

    // Add error handling for images
    document.querySelectorAll('img').forEach(img => {
        // Ensure all images are visible initially
        if (img.style.opacity === '0' || img.style.opacity === '') {
            img.style.opacity = '1';
        }

        img.addEventListener('error', function() {
            // Handle image loading errors gracefully
            this.style.minHeight = '100px';
            this.style.backgroundColor = '#f5f5f5';
            this.style.opacity = '1';
        });

        img.addEventListener('load', function() {
            this.style.opacity = '1';
        });

        // Force load images that might be stuck
        if (!img.complete && img.src) {
            // Trigger a reload by setting src again
            const originalSrc = img.src;
            img.src = '';
            setTimeout(() => {
                img.src = originalSrc;
            }, 10);
        }
    });
}

/**
 * Initialize image enlargement functionality
 */
function initImageEnlargement() {
    // Create modal for image enlargement
    const modal = document.createElement('div');
    modal.className = 'image-modal';
    modal.innerHTML = `
        <div class="image-modal-overlay">
            <div class="image-modal-content">
                <button class="image-modal-close" aria-label="Close image">&times;</button>
                <img class="image-modal-img" src="" alt="">
            </div>
        </div>
    `;
    document.body.appendChild(modal);

    // Add CSS for modal
    const modalStyles = document.createElement('style');
    modalStyles.textContent = `
        .image-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10000;
            background: rgba(0, 0, 0, 0.9);
        }

        .image-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .image-modal-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
        }

        .image-modal-img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .image-modal-close {
            position: absolute;
            top: -40px;
            right: 0;
            background: none;
            border: none;
            color: white;
            font-size: 2rem;
            cursor: pointer;
            padding: 0.5rem;
            line-height: 1;
        }

        .image-modal-close:hover {
            opacity: 0.7;
        }
    `;
    document.head.appendChild(modalStyles);

    // Add click handlers to product images (exclude pages with their own lightbox)
    // Check if we're on a product page with existing lightbox functionality
    const hasExistingLightbox = document.getElementById('productLightbox') !== null;

    if (!hasExistingLightbox) {
        document.querySelectorAll('.product-image img, .carousel-slide img').forEach(img => {
            img.style.cursor = 'pointer';
            img.addEventListener('click', function() {
                const modalImg = modal.querySelector('.image-modal-img');
                modalImg.src = this.src;
                modalImg.alt = this.alt;
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
            });
        });
    } else {
        // Only add handlers to carousel images if lightbox exists
        document.querySelectorAll('.carousel-slide img').forEach(img => {
            img.style.cursor = 'pointer';
            img.addEventListener('click', function() {
                const modalImg = modal.querySelector('.image-modal-img');
                modalImg.src = this.src;
                modalImg.alt = this.alt;
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
            });
        });
    }

    // Close modal functionality
    const closeModal = () => {
        modal.classList.remove('active');
        document.body.style.overflow = '';
    };

    modal.querySelector('.image-modal-close').addEventListener('click', closeModal);
    modal.querySelector('.image-modal-overlay').addEventListener('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });

    // Close with ESC key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.classList.contains('active')) {
            closeModal();
        }
    });
}

/**
 * Initialize mobile menu functionality
 */
function initMobileMenu() {
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navLinks = document.querySelector('.nav-links');
    const dropdowns = document.querySelectorAll('.dropdown');

    if (mobileMenuBtn && navLinks) {
        mobileMenuBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            navLinks.classList.toggle('show');
            const isOpen = navLinks.classList.contains('show');

            // Update button icon
            const icon = mobileMenuBtn.querySelector('i');
            if (icon) {
                icon.className = isOpen ? 'fas fa-times' : 'fas fa-bars';
            }

            // Prevent body scroll when menu is open
            document.body.style.overflow = isOpen ? 'hidden' : '';
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') &&
                !navLinks.contains(e.target) &&
                !mobileMenuBtn.contains(e.target)) {

                navLinks.classList.remove('show');

                const icon = mobileMenuBtn.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }

                document.body.style.overflow = '';
            }
        });

        // Close menu when clicking on nav links (mobile)
        navLinks.addEventListener('click', function(e) {
            if (window.innerWidth <= 768 && e.target.tagName === 'A' && !e.target.closest('.dropdown')) {
                navLinks.classList.remove('show');

                const icon = mobileMenuBtn.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }

                document.body.style.overflow = '';
            }
        });

        // Handle dropdown menus (both mobile and desktop)
        dropdowns.forEach(dropdown => {
            const dropdownLink = dropdown.querySelector('a');

            if (dropdownLink) {
                dropdownLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    dropdown.classList.toggle('open');

                    // Close other dropdowns
                    dropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown && otherDropdown.classList.contains('open')) {
                            otherDropdown.classList.remove('open');
                        }
                    });
                });
            }
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.dropdown')) {
                dropdowns.forEach(dropdown => {
                    dropdown.classList.remove('open');
                });
            }
        });

        // Reset dropdowns and menu on window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                navLinks.classList.remove('show');
                document.body.style.overflow = '';

                const icon = mobileMenuBtn.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }

                dropdowns.forEach(dropdown => {
                    dropdown.classList.remove('open');
                });
            }
        });

        // Handle escape key to close menu
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && navLinks.classList.contains('show')) {
                navLinks.classList.remove('show');

                const icon = mobileMenuBtn.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }

                document.body.style.overflow = '';
            }
        });
    }
}

/**
 * Initialize back to top button with throttled scroll event
 */
function initBackToTop() {
    const backToTop = document.querySelector('.back-to-top');
    if (!backToTop) return;

    // Throttle scroll event for better performance
    let scrollTimeout;
    window.addEventListener('scroll', function() {
        if (!scrollTimeout) {
            scrollTimeout = setTimeout(function() {
                if (window.pageYOffset > 100) {
                    backToTop.classList.add('visible');
                } else {
                    backToTop.classList.remove('visible');
                }
                scrollTimeout = null;
            }, 100);
        }
    }, { passive: true });

    backToTop.addEventListener('click', function(e) {
        e.preventDefault();
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });
}

/**
 * Initialize product animations with Intersection Observer
 */
function initProductAnimations() {
    const productItems = document.querySelectorAll('.product-item');

    if (productItems.length && 'IntersectionObserver' in window) {
        const productObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                    productObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1, rootMargin: '50px' });

        productItems.forEach(item => {
            productObserver.observe(item);
        });
    } else {
        // Fallback for browsers that don't support Intersection Observer
        productItems.forEach(item => {
            item.classList.add('animated');
        });
    }
}

/**
 * Initialize carousel functionality
 * @param {Object} options - Configuration options
 */
function initCarousel(options = {}) {
    const carousel = document.querySelector('.carousel');
    if (!carousel) return;

    const slides = document.querySelectorAll('.carousel-slide');
    const dots = document.querySelectorAll('.dot');
    const prevBtn = document.querySelector('.carousel-prev');
    const nextBtn = document.querySelector('.carousel-next');

    if (!slides.length) return;

    let currentSlide = 0;
    let autoAdvanceTimer = null;

    // Default options
    const settings = {
        autoAdvanceInterval: 5000,
        pauseOnHover: true,
        ...options
    };

    /**
     * Show a specific slide
     * @param {number} index - Index of the slide to show
     */
    function showSlide(index) {
        slides.forEach(slide => slide.classList.remove('active'));
        dots.forEach(dot => dot.classList.remove('active'));

        currentSlide = (index + slides.length) % slides.length;
        slides[currentSlide].classList.add('active');

        if (dots.length) {
            dots[currentSlide].classList.add('active');
        }
    }

    /**
     * Start auto-advance timer
     */
    function startAutoAdvance() {
        if (settings.autoAdvanceInterval > 0) {
            clearInterval(autoAdvanceTimer);
            autoAdvanceTimer = setInterval(() => {
                showSlide(currentSlide + 1);
            }, settings.autoAdvanceInterval);
        }
    }

    /**
     * Reset auto-advance timer
     */
    function resetAutoAdvance() {
        clearInterval(autoAdvanceTimer);
        startAutoAdvance();
    }

    // Event listeners for carousel controls
    if (prevBtn && nextBtn) {
        prevBtn.addEventListener('click', (e) => {
            e.preventDefault();
            showSlide(currentSlide - 1);
            resetAutoAdvance();
        });

        nextBtn.addEventListener('click', (e) => {
            e.preventDefault();
            showSlide(currentSlide + 1);
            resetAutoAdvance();
        });
    }

    // Event listeners for dots
    if (dots.length) {
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                showSlide(index);
                resetAutoAdvance();
            });
        });
    }

    // Touch events for mobile swipe
    let touchStartX = 0;
    let touchEndX = 0;

    carousel.addEventListener('touchstart', (e) => {
        touchStartX = e.changedTouches[0].screenX;
        clearInterval(autoAdvanceTimer);
    }, { passive: true });

    carousel.addEventListener('touchend', (e) => {
        touchEndX = e.changedTouches[0].screenX;
        const swipeThreshold = 50;

        // Swipe left (next slide)
        if (touchStartX - touchEndX > swipeThreshold) {
            showSlide(currentSlide + 1);
        }

        // Swipe right (previous slide)
        if (touchEndX - touchStartX > swipeThreshold) {
            showSlide(currentSlide - 1);
        }

        resetAutoAdvance();
    }, { passive: true });

    // Pause auto-advance on hover (desktop only)
    if (settings.pauseOnHover) {
        carousel.addEventListener('mouseenter', () => {
            clearInterval(autoAdvanceTimer);
        });

        carousel.addEventListener('mouseleave', () => {
            startAutoAdvance();
        });
    }

    // Handle visibility change (tab switching)
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            clearInterval(autoAdvanceTimer);
        } else {
            startAutoAdvance();
        }
    });

    // Initialize
    showSlide(0);
    startAutoAdvance();
}

/**
 * Initialize feature animations with Intersection Observer
 */
function initFeatureAnimations() {
    const features = document.querySelectorAll('.feature');

    if (features.length && 'IntersectionObserver' in window) {
        const featureObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                    featureObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1, rootMargin: '50px' });

        features.forEach(feature => {
            featureObserver.observe(feature);
        });
    } else {
        // Fallback for browsers that don't support Intersection Observer
        features.forEach(feature => {
            feature.classList.add('animated');
        });
    }
}

/**
 * Initialize FAQ accordion functionality
 */
function initFaqAccordion() {
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');

        question.addEventListener('click', () => {
            // Toggle active class on the clicked item
            item.classList.toggle('active');

            // Close other items
            faqItems.forEach(otherItem => {
                if (otherItem !== item && otherItem.classList.contains('active')) {
                    otherItem.classList.remove('active');
                }
            });
        });
    });
}

/**
 * Initialize value items animations with Intersection Observer
 */
function initValueAnimations() {
    const valueItems = document.querySelectorAll('.value-item');

    if (valueItems.length && 'IntersectionObserver' in window) {
        const valueObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    // Add delay based on index for staggered animation
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 150);

                    valueObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        valueItems.forEach(item => {
            // Set initial styles
            item.style.opacity = '0';
            item.style.transform = 'translateY(30px)';
            item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

            valueObserver.observe(item);
        });
    } else {
        // Fallback for browsers that don't support Intersection Observer
        valueItems.forEach(item => {
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        });
    }
}

/**
 * Initialize contact form functionality
 */
function initContactForm() {
    const contactForm = document.getElementById('contactForm');
    const formSuccess = document.querySelector('.form-success');
    const sendAnother = document.getElementById('sendAnother');

    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Simulate form submission (in a real application, you would send the data to a server)
            setTimeout(() => {
                contactForm.style.display = 'none';
                formSuccess.style.display = 'block';
            }, 1000);
        });
    }

    if (sendAnother) {
        sendAnother.addEventListener('click', function() {
            formSuccess.style.display = 'none';
            contactForm.style.display = 'flex';
            contactForm.reset();
        });
    }
}

/**
 * Initialize location card animations with Intersection Observer
 */
function initLocationCardAnimations() {
    const cards = document.querySelectorAll('.location-card, .contact-card');

    if (cards.length && 'IntersectionObserver' in window) {
        const cardObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    // Add delay based on index for staggered animation
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 150);

                    cardObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        cards.forEach(card => {
            // Set initial styles
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

            cardObserver.observe(card);
        });
    } else {
        // Fallback for browsers that don't support Intersection Observer
        cards.forEach(card => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        });
    }
}

/**
 * Initialize team member animations with Intersection Observer
 */
function initTeamAnimations() {
    const teamMembers = document.querySelectorAll('.team-member');

    if (teamMembers.length && 'IntersectionObserver' in window) {
        const teamObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    // Add delay based on index for staggered animation
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 200);

                    teamObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        teamMembers.forEach(member => {
            // Set initial styles
            member.style.opacity = '0';
            member.style.transform = 'translateY(30px)';
            member.style.transition = 'opacity 0.8s ease, transform 0.8s ease';

            teamObserver.observe(member);
        });
    } else {
        // Fallback for browsers that don't support Intersection Observer
        teamMembers.forEach(member => {
            member.style.opacity = '1';
            member.style.transform = 'translateY(0)';
        });
    }
}

/**
 * Initialize stats counter with Intersection Observer
 */
function initStatsCounter() {
    const stats = document.querySelectorAll('.stat-number');
    if (!stats.length) return;

    // Group stats by their containers for better animation control
    const statGroups = {};

    stats.forEach(stat => {
        // Find the closest container
        const container = stat.closest('.intro-stats') ||
                         stat.closest('.company-achievements') ||
                         stat.closest('.trust-metrics') ||
                         stat.closest('.plant-stats') ||
                         stat.closest('.metric-item') ||
                         stat.closest('.achievement-item') ||
                         'default';

        // Create a group for this container if it doesn't exist
        if (!statGroups[container]) {
            statGroups[container] = [];
        }

        // Add the stat to its container group
        statGroups[container].push(stat);
    });

    function animateStats(statsToAnimate) {
        statsToAnimate.forEach(stat => {
            const target = parseInt(stat.getAttribute('data-count'));
            if (isNaN(target)) return;

            let current = 0;
            // Adjust speed based on target value
            const duration = 2000; // 2 seconds for all animations
            const steps = 60; // 60 steps (for 60fps)
            const increment = target / steps;
            const interval = duration / steps;

            const timer = setInterval(() => {
                current += increment;

                // Format large numbers with commas
                if (target > 999) {
                    stat.textContent = Math.floor(current).toLocaleString();
                } else {
                    stat.textContent = Math.floor(current);
                }

                if (current >= target) {
                    if (target > 999) {
                        stat.textContent = target.toLocaleString();
                    } else {
                        stat.textContent = target;
                    }
                    clearInterval(timer);
                }
            }, interval);
        });
    }

    // Use Intersection Observer to trigger animation when stats are in view
    if ('IntersectionObserver' in window) {
        const statsObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Animate all stats in the container that was observed
                    if (entry.target.classList.contains('stat-number')) {
                        // If we're observing the stat directly
                        animateStats([entry.target]);
                    } else {
                        // If we're observing a container
                        const statsInContainer = entry.target.querySelectorAll('.stat-number');
                        if (statsInContainer.length > 0) {
                            animateStats(Array.from(statsInContainer));
                        }
                    }
                    statsObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1, rootMargin: '0px 0px -100px 0px' });

        // Create a set of containers to observe
        const containersToObserve = new Set();

        // Find all containers with stats
        stats.forEach(stat => {
            const container = stat.closest('.intro-stats') ||
                             stat.closest('.company-achievements') ||
                             stat.closest('.trust-metrics') ||
                             stat.closest('.plant-stats') ||
                             stat.closest('.metric-item') ||
                             stat.closest('.achievement-item');

            if (container) {
                containersToObserve.add(container);
            } else {
                // If no container found, observe the stat itself
                statsObserver.observe(stat);
            }
        });

        // Observe each unique container
        containersToObserve.forEach(container => {
            statsObserver.observe(container);
        });
    } else {
        // Fallback for browsers that don't support Intersection Observer
        animateStats(Array.from(stats));
    }
}

/**
 * Initialize footer dropdown functionality
 */
function initFooterDropdowns() {
    const footerDropdowns = document.querySelectorAll('.footer-dropdown');

    footerDropdowns.forEach(dropdown => {
        const toggle = dropdown.querySelector('.footer-dropdown-toggle');
        const menu = dropdown.querySelector('.footer-dropdown-menu');

        if (!toggle || !menu) return;

        // Toggle dropdown on click
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const isOpen = dropdown.classList.contains('open');

            // Close all other footer dropdowns first
            footerDropdowns.forEach(otherDropdown => {
                if (otherDropdown !== dropdown) {
                    otherDropdown.classList.remove('open');
                    const otherToggle = otherDropdown.querySelector('.footer-dropdown-toggle');
                    if (otherToggle) {
                        otherToggle.setAttribute('aria-expanded', 'false');
                    }
                }
            });

            // Toggle current dropdown
            if (isOpen) {
                dropdown.classList.remove('open');
                toggle.setAttribute('aria-expanded', 'false');
            } else {
                dropdown.classList.add('open');
                toggle.setAttribute('aria-expanded', 'true');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!dropdown.contains(e.target)) {
                dropdown.classList.remove('open');
                toggle.setAttribute('aria-expanded', 'false');
            }
        });

        // Close dropdown on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && dropdown.classList.contains('open')) {
                dropdown.classList.remove('open');
                toggle.setAttribute('aria-expanded', 'false');
                toggle.focus();
            }
        });

        // Handle keyboard navigation within dropdown
        toggle.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                toggle.click();
            } else if (e.key === 'ArrowDown') {
                e.preventDefault();
                if (!dropdown.classList.contains('open')) {
                    toggle.click();
                }
                // Focus first menu item
                const firstItem = menu.querySelector('a');
                if (firstItem) firstItem.focus();
            }
        });

        // Handle keyboard navigation within menu
        const menuItems = menu.querySelectorAll('a');
        menuItems.forEach((item, index) => {
            item.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    const nextItem = menuItems[index + 1] || menuItems[0];
                    nextItem.focus();
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    const prevItem = menuItems[index - 1] || menuItems[menuItems.length - 1];
                    prevItem.focus();
                } else if (e.key === 'Escape') {
                    e.preventDefault();
                    dropdown.classList.remove('open');
                    toggle.setAttribute('aria-expanded', 'false');
                    toggle.focus();
                }
            });
        });
    });
}
